const { EmbedBuilder } = require("discord.js");
const config = require("../config/config");

/**
 * T<PERSON><PERSON> embed thành công
 */
function createSuccessEmbed(title, description, fields = []) {
  const embed = new EmbedBuilder()
    .setColor(config.colors.success)
    .setTitle(`${config.emojis.success} ${title}`)
    .setDescription(description)
    .setTimestamp()
    .setFooter({ text: "Economy Bot" });

  if (fields.length > 0) {
    embed.addFields(fields);
  }

  return embed;
}

/**
 * Tạo embed lỗi
 */
function createErrorEmbed(title, description, fields = []) {
  const embed = new EmbedBuilder()
    .setColor(config.colors.error)
    .setTitle(`${config.emojis.error} ${title}`)
    .setDescription(description)
    .setTimestamp()
    .setFooter({ text: "Economy Bot" });

  if (fields.length > 0) {
    embed.addFields(fields);
  }

  return embed;
}

/**
 * Tạo embed cảnh báo
 */
function createWarningEmbed(title, description, fields = []) {
  const embed = new EmbedBuilder()
    .setColor(config.colors.warning)
    .setTitle(`${config.emojis.warning} ${title}`)
    .setDescription(description)
    .setTimestamp()
    .setFooter({ text: "Economy Bot" });

  if (fields.length > 0) {
    embed.addFields(fields);
  }

  return embed;
}

/**
 * Tạo embed thông tin
 */
function createInfoEmbed(title, description, fields = []) {
  const embed = new EmbedBuilder()
    .setColor(config.colors.info)
    .setTitle(`${config.emojis.info} ${title}`)
    .setDescription(description)
    .setTimestamp()
    .setFooter({ text: "Economy Bot" });

  if (fields.length > 0) {
    embed.addFields(fields);
  }

  return embed;
}

/**
 * Tạo embed kinh tế
 */
function createEconomyEmbed(title, description, fields = []) {
  const embed = new EmbedBuilder()
    .setColor(config.colors.economy)
    .setTitle(`${config.emojis.money} ${title}`)
    .setDescription(description)
    .setTimestamp()
    .setFooter({ text: "Economy Bot" });

  if (fields.length > 0) {
    embed.addFields(fields);
  }

  return embed;
}

/**
 * Tạo embed profile người dùng
 */
function createProfileEmbed(user, userData) {
  const totalWealth = userData.balance + userData.bank;

  // Tính toán thông tin level
  const levelInfo = userData.getLevelInfo();
  const progressBar = createProgressBar(levelInfo.progress, 10);

  const embed = new EmbedBuilder()
    .setColor(config.colors.primary)
    .setTitle(`${config.emojis.crown} Hồ sơ của ${user.username}`)
    .setThumbnail(user.displayAvatarURL({ dynamic: true }))
    .addFields(
      {
        name: `${config.emojis.money} Số dư`,
        value: `${formatCurrency(userData.balance)}`,
        inline: true,
      },
      {
        name: `${config.emojis.bank} Ngân hàng`,
        value: `${formatCurrency(userData.bank)}`,
        inline: true,
      },
      {
        name: `${config.emojis.gem} Tổng tài sản`,
        value: `${formatCurrency(totalWealth)}`,
        inline: true,
      },
      {
        name: `⭐ Level ${userData.level}`,
        value: `${progressBar}\n${levelInfo.progressExp}/${levelInfo.neededExp} EXP (${levelInfo.progress}%)`,
        inline: false,
      },
      {
        name: `${config.emojis.star} Thống kê`,
        value:
          `**Tổng kiếm được:** ${formatCurrency(userData.totalEarned)}\n` +
          `**Tổng chi tiêu:** ${formatCurrency(userData.totalSpent)}\n` +
          `**Daily streak:** ${userData.streakDaily} ngày`,
        inline: true,
      },
      {
        name: `${config.emojis.info} Thông tin`,
        value:
          `**Prestige:** ${userData.prestige}\n` +
          `**Tham gia:** <t:${Math.floor(
            new Date(userData.createdAt).getTime() / 1000
          )}:R>\n` +
          `**Cập nhật:** <t:${Math.floor(
            new Date(userData.updatedAt).getTime() / 1000
          )}:R>`,
        inline: true,
      }
    )
    .setTimestamp()
    .setFooter({ text: "Economy Bot" });

  return embed;
}

/**
 * Tạo thanh tiến độ
 */
function createProgressBar(percentage, length = 10) {
  const filled = Math.round((percentage / 100) * length);
  const empty = length - filled;
  return "█".repeat(filled) + "░".repeat(empty);
}

/**
 * Tạo embed leaderboard
 */
function createLeaderboardEmbed(users, type = "wealth") {
  let title, description;

  switch (type) {
    case "wealth":
      title = `${config.emojis.trophy} Bảng xếp hạng tài sản`;
      description = "Top 10 người dùng giàu nhất";
      break;
    default:
      title = `${config.emojis.trophy} Bảng xếp hạng`;
      description = "Top 10 người dùng";
  }

  const embed = new EmbedBuilder()
    .setColor(config.colors.economy)
    .setTitle(title)
    .setDescription(description)
    .setTimestamp()
    .setFooter({ text: "Economy Bot" });

  if (users.length === 0) {
    embed.addFields({
      name: "Không có dữ liệu",
      value: "Chưa có người dùng nào trong bảng xếp hạng",
    });
  } else {
    const leaderboardText = users
      .map((user, index) => {
        const medal =
          index === 0
            ? "🥇"
            : index === 1
            ? "🥈"
            : index === 2
            ? "🥉"
            : `${index + 1}.`;
        const wealth = user.total_wealth || user.balance + user.bank;
        return `${medal} **${user.username}** - ${formatCurrency(wealth)}`;
      })
      .join("\n");

    embed.addFields({
      name: "Xếp hạng",
      value: leaderboardText,
    });
  }

  return embed;
}

/**
 * Format số tiền
 */
function formatCurrency(amount) {
  return `${amount.toLocaleString("vi-VN")} ${config.economy.currency.symbol}`;
}

/**
 * Format số với dấu phẩy
 */
function formatNumber(number) {
  return number.toLocaleString("vi-VN");
}

module.exports = {
  createSuccessEmbed,
  createErrorEmbed,
  createWarningEmbed,
  createInfoEmbed,
  createEconomyEmbed,
  createProfileEmbed,
  createLeaderboardEmbed,
  createProgressBar,
  formatCurrency,
  formatNumber,
};
