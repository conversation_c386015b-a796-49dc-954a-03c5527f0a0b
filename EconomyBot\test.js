const { deployCommands } = require('./src/utils/deployCommands');

async function test() {
    try {
        console.log('🚀 Bắt đầu deploy commands...');
        await deployCommands();
        console.log('✅ Deploy commands thành công!');
        
        // Test database connection
        const database = require('./src/database/database');
        await database.init();
        console.log('✅ Database connection thành công!');
        
        console.log('🎉 Tất cả tests đã pass!');
        process.exit(0);
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}

test();
