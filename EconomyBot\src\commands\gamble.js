const { SlashCommandBuilder } = require('discord.js');
const User = require('../models/User');
const { parseAmount, validateAmount } = require('../utils/economy');
const { createSuccessEmbed, createErrorEmbed, formatCurrency } = require('../utils/embedBuilder');
const config = require('../config/config');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('gamble')
        .setNameLocalizations({
            'vi': 'cuabac'
        })
        .setDescription('Đánh bạc với nhiều trò chơi khác nhau')
        .addSubcommand(subcommand =>
            subcommand
                .setName('coinflip')
                .setNameLocalizations({
                    'vi': 'tossxu'
                })
                .setDescription('Tung xu - Đoán ngửa hay sấp')
                .addStringOption(option =>
                    option.setName('amount')
                        .setNameLocalizations({
                            'vi': 'sotien'
                        })
                        .setDescription('Số tiền đặt cược')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('choice')
                        .setNameLocalizations({
                            'vi': 'luachon'
                        })
                        .setDescription('Chọn ngửa hay sấp')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Ngửa (Heads)', value: 'heads' },
                            { name: 'Sấp (Tails)', value: 'tails' }
                        )
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('dice')
                .setNameLocalizations({
                    'vi': 'xucxac'
                })
                .setDescription('Lắc xúc xắc - Đoán số từ 1-6')
                .addStringOption(option =>
                    option.setName('amount')
                        .setNameLocalizations({
                            'vi': 'sotien'
                        })
                        .setDescription('Số tiền đặt cược')
                        .setRequired(true)
                )
                .addIntegerOption(option =>
                    option.setName('number')
                        .setNameLocalizations({
                            'vi': 'so'
                        })
                        .setDescription('Đoán số từ 1 đến 6')
                        .setRequired(true)
                        .setMinValue(1)
                        .setMaxValue(6)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('slots')
                .setNameLocalizations({
                    'vi': 'mayquay'
                })
                .setDescription('Máy quay số - Jackpot x10')
                .addStringOption(option =>
                    option.setName('amount')
                        .setNameLocalizations({
                            'vi': 'sotien'
                        })
                        .setDescription('Số tiền đặt cược')
                        .setRequired(true)
                )
        ),
    
    cooldown: 10,

    async execute(interaction) {
        try {
            const subcommand = interaction.options.getSubcommand();
            const userData = await User.findOrCreate(interaction.user);

            switch (subcommand) {
                case 'coinflip':
                    await handleCoinflip(interaction, userData);
                    break;
                case 'dice':
                    await handleDice(interaction, userData);
                    break;
                case 'slots':
                    await handleSlots(interaction, userData);
                    break;
            }
            
        } catch (error) {
            console.error('Lỗi lệnh gamble:', error);
            
            const errorEmbed = createErrorEmbed(
                'Lỗi casino',
                'Không thể thực hiện đánh bạc. Máy chủ casino đang bảo trì!'
            );
            
            await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }
    },
};

async function handleCoinflip(interaction, userData) {
    const amountStr = interaction.options.getString('amount');
    const choice = interaction.options.getString('choice');
    
    try {
        // Parse và validate số tiền
        let amount = parseAmount(amountStr);
        if (amount === 'all') amount = userData.balance;
        if (amount === 'half') amount = Math.floor(userData.balance / 2);
        
        amount = validateAmount(amount, userData.balance);
        
        if (amount < 10) {
            const errorEmbed = createErrorEmbed(
                'Số tiền quá nhỏ',
                'Số tiền đặt cược tối thiểu là 10 xu!'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Tung xu
        const result = Math.random() < 0.5 ? 'heads' : 'tails';
        const won = choice === result;
        
        const resultEmoji = result === 'heads' ? '🪙' : '🔘';
        const choiceText = choice === 'heads' ? 'Ngửa' : 'Sấp';
        const resultText = result === 'heads' ? 'Ngửa' : 'Sấp';

        if (won) {
            const winAmount = Math.floor(amount * 1.8); // 80% lợi nhuận
            await userData.updateBalance(winAmount - amount, 'gamble_win');
            await userData.addExperience(Math.floor(amount / 20));

            const winEmbed = createSuccessEmbed(
                'Thắng cược!',
                `${resultEmoji} **Kết quả:** ${resultText}\n` +
                `🎯 **Dự đoán:** ${choiceText}\n\n` +
                `Bạn đã thắng **${formatCurrency(winAmount - amount)}**!\n\n` +
                `${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(userData.balance + winAmount - amount)}`
            );

            await interaction.reply({ embeds: [winEmbed] });
        } else {
            await userData.updateBalance(-amount, 'gamble_loss');

            const loseEmbed = createErrorEmbed(
                'Thua cược!',
                `${resultEmoji} **Kết quả:** ${resultText}\n` +
                `🎯 **Dự đoán:** ${choiceText}\n\n` +
                `Bạn đã mất **${formatCurrency(amount)}**!\n\n` +
                `${config.emojis.money} **Số dư còn lại:** ${formatCurrency(userData.balance - amount)}`
            );

            await interaction.reply({ embeds: [loseEmbed] });
        }
        
    } catch (error) {
        const errorEmbed = createErrorEmbed(
            'Lỗi đặt cược',
            error.message || 'Không thể thực hiện cược tung xu'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleDice(interaction, userData) {
    const amountStr = interaction.options.getString('amount');
    const guessNumber = interaction.options.getInteger('number');
    
    try {
        // Parse và validate số tiền
        let amount = parseAmount(amountStr);
        if (amount === 'all') amount = userData.balance;
        if (amount === 'half') amount = Math.floor(userData.balance / 2);
        
        amount = validateAmount(amount, userData.balance);
        
        if (amount < 10) {
            const errorEmbed = createErrorEmbed(
                'Số tiền quá nhỏ',
                'Số tiền đặt cược tối thiểu là 10 xu!'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Lắc xúc xắc
        const result = Math.floor(Math.random() * 6) + 1;
        const won = guessNumber === result;

        if (won) {
            const winAmount = amount * 5; // x5 nếu đoán đúng
            await userData.updateBalance(winAmount - amount, 'gamble_win');
            await userData.addExperience(Math.floor(amount / 10));

            const winEmbed = createSuccessEmbed(
                'Jackpot xúc xắc!',
                `🎲 **Kết quả:** ${result}\n` +
                `🎯 **Dự đoán:** ${guessNumber}\n\n` +
                `🎉 **ĐOÁN ĐÚNG!** Bạn thắng **${formatCurrency(winAmount - amount)}**!\n\n` +
                `${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(userData.balance + winAmount - amount)}`
            );

            await interaction.reply({ embeds: [winEmbed] });
        } else {
            await userData.updateBalance(-amount, 'gamble_loss');

            const loseEmbed = createErrorEmbed(
                'Chưa may mắn!',
                `🎲 **Kết quả:** ${result}\n` +
                `🎯 **Dự đoán:** ${guessNumber}\n\n` +
                `Bạn đã mất **${formatCurrency(amount)}**!\n\n` +
                `${config.emojis.money} **Số dư còn lại:** ${formatCurrency(userData.balance - amount)}`
            );

            await interaction.reply({ embeds: [loseEmbed] });
        }
        
    } catch (error) {
        const errorEmbed = createErrorEmbed(
            'Lỗi đặt cược',
            error.message || 'Không thể thực hiện cược xúc xắc'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}

async function handleSlots(interaction, userData) {
    const amountStr = interaction.options.getString('amount');
    
    try {
        // Parse và validate số tiền
        let amount = parseAmount(amountStr);
        if (amount === 'all') amount = userData.balance;
        if (amount === 'half') amount = Math.floor(userData.balance / 2);
        
        amount = validateAmount(amount, userData.balance);
        
        if (amount < 50) {
            const errorEmbed = createErrorEmbed(
                'Số tiền quá nhỏ',
                'Số tiền đặt cược tối thiểu cho máy quay là 50 xu!'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        // Các biểu tượng máy quay
        const symbols = ['🍒', '🍋', '🍊', '🍇', '⭐', '💎', '7️⃣'];
        const weights = [30, 25, 20, 15, 7, 2, 1]; // Tỷ lệ xuất hiện
        
        // Quay 3 ô
        const results = [];
        for (let i = 0; i < 3; i++) {
            const random = Math.random() * 100;
            let cumulative = 0;
            for (let j = 0; j < symbols.length; j++) {
                cumulative += weights[j];
                if (random <= cumulative) {
                    results.push(symbols[j]);
                    break;
                }
            }
        }

        // Tính toán kết quả
        let multiplier = 0;
        let winType = '';

        if (results[0] === results[1] && results[1] === results[2]) {
            // Ba ô giống nhau
            switch (results[0]) {
                case '🍒': multiplier = 2; winType = 'Ba cherry!'; break;
                case '🍋': multiplier = 3; winType = 'Ba chanh!'; break;
                case '🍊': multiplier = 4; winType = 'Ba cam!'; break;
                case '🍇': multiplier = 5; winType = 'Ba nho!'; break;
                case '⭐': multiplier = 8; winType = 'Ba sao!'; break;
                case '💎': multiplier = 15; winType = 'Ba kim cương!'; break;
                case '7️⃣': multiplier = 50; winType = 'JACKPOT 777!'; break;
            }
        } else if (results[0] === results[1] || results[1] === results[2] || results[0] === results[2]) {
            // Hai ô giống nhau
            multiplier = 1.5;
            winType = 'Hai ô trùng!';
        }

        const resultDisplay = `🎰 | ${results[0]} | ${results[1]} | ${results[2]} | 🎰`;

        if (multiplier > 0) {
            const winAmount = Math.floor(amount * multiplier) - amount;
            await userData.updateBalance(winAmount, 'gamble_win');
            await userData.addExperience(Math.floor(amount / 15));

            const winEmbed = createSuccessEmbed(
                winType,
                `${resultDisplay}\n\n` +
                `🎉 **Thắng lớn!** Hệ số: x${multiplier}\n` +
                `Bạn thắng **${formatCurrency(winAmount)}**!\n\n` +
                `${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(userData.balance + winAmount)}`
            );

            await interaction.reply({ embeds: [winEmbed] });
        } else {
            await userData.updateBalance(-amount, 'gamble_loss');

            const loseEmbed = createErrorEmbed(
                'Không trúng thưởng!',
                `${resultDisplay}\n\n` +
                `Bạn đã mất **${formatCurrency(amount)}**!\n\n` +
                `${config.emojis.money} **Số dư còn lại:** ${formatCurrency(userData.balance - amount)}\n\n` +
                `💡 **Mẹo:** Thử lại để có cơ hội trúng Jackpot x50!`
            );

            await interaction.reply({ embeds: [loseEmbed] });
        }
        
    } catch (error) {
        const errorEmbed = createErrorEmbed(
            'Lỗi đặt cược',
            error.message || 'Không thể thực hiện máy quay'
        );
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
}
