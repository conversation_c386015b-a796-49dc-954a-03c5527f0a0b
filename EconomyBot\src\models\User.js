const db = require("../database/database");
const config = require("../config/config");

class User {
  constructor(data) {
    this.id = data.id;
    this.username = data.username;
    this.discriminator = data.discriminator;
    this.avatar = data.avatar;
    this.balance = data.balance || 0;
    this.bank = data.bank || 0;
    this.totalEarned = data.total_earned || 0;
    this.totalSpent = data.total_spent || 0;
    this.lastDaily = data.last_daily;
    this.lastWork = data.last_work;
    this.lastCrime = data.last_crime;
    this.lastRob = data.last_rob;
    this.streakDaily = data.streak_daily || 0;
    this.streakWork = data.streak_work || 0;
    this.level = data.level || 1;
    this.experience = data.experience || 0;
    this.prestige = data.prestige || 0;
    this.createdAt = data.created_at;
    this.updatedAt = data.updated_at;
  }

  // Tạo hoặc lấy thông tin user
  static async findOrCreate(discordUser) {
    try {
      let user = await db.get("SELECT * FROM users WHERE id = ?", [
        discordUser.id,
      ]);

      if (!user) {
        // Tạo user mới với số dư ban đầu
        await db.run(
          `
                    INSERT INTO users (id, username, discriminator, avatar, balance, bank)
                    VALUES (?, ?, ?, ?, ?, ?)
                `,
          [
            discordUser.id,
            discordUser.username,
            discordUser.discriminator || "0000",
            discordUser.avatar,
            config.economy.startingBalance,
            0,
          ]
        );

        user = await db.get("SELECT * FROM users WHERE id = ?", [
          discordUser.id,
        ]);

        // Ghi log giao dịch khởi tạo
        await db.run(
          `
                    INSERT INTO transactions (user_id, type, amount, description)
                    VALUES (?, ?, ?, ?)
                `,
          [
            discordUser.id,
            "initial",
            config.economy.startingBalance,
            "Số dư ban đầu",
          ]
        );
      }

      return new User(user);
    } catch (error) {
      console.error("Lỗi tạo/tìm user:", error);
      throw error;
    }
  }

  // Lấy thông tin user theo ID
  static async findById(userId) {
    try {
      const user = await db.get("SELECT * FROM users WHERE id = ?", [userId]);
      return user ? new User(user) : null;
    } catch (error) {
      console.error("Lỗi tìm user:", error);
      throw error;
    }
  }

  // Cập nhật số dư
  async updateBalance(amount, type = "manual") {
    try {
      const newBalance = this.balance + amount;
      if (newBalance < 0) {
        throw new Error("Số dư không đủ");
      }

      await db.run(
        "UPDATE users SET balance = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
        [newBalance, this.id]
      );

      // Ghi log giao dịch
      await db.run(
        `
                INSERT INTO transactions (user_id, type, amount, description)
                VALUES (?, ?, ?, ?)
            `,
        [
          this.id,
          type,
          amount,
          `Thay đổi số dư: ${amount > 0 ? "+" : ""}${amount}`,
        ]
      );

      // Cập nhật thống kê
      if (amount > 0) {
        await db.run(
          "UPDATE users SET total_earned = total_earned + ? WHERE id = ?",
          [amount, this.id]
        );
      } else {
        await db.run(
          "UPDATE users SET total_spent = total_spent + ? WHERE id = ?",
          [Math.abs(amount), this.id]
        );
      }

      this.balance = newBalance;
      return this.balance;
    } catch (error) {
      console.error("Lỗi cập nhật số dư:", error);
      throw error;
    }
  }

  // Chuyển tiền vào ngân hàng
  async deposit(amount) {
    try {
      if (amount <= 0) throw new Error("Số tiền phải lớn hơn 0");
      if (this.balance < amount) throw new Error("Số dư không đủ");

      await db.run(
        `
                UPDATE users SET 
                    balance = balance - ?, 
                    bank = bank + ?,
                    updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            `,
        [amount, amount, this.id]
      );

      // Ghi log giao dịch
      await db.run(
        `
                INSERT INTO transactions (user_id, type, amount, description)
                VALUES (?, ?, ?, ?)
            `,
        [this.id, "deposit", amount, `Gửi tiền vào ngân hàng: ${amount}`]
      );

      this.balance -= amount;
      this.bank += amount;
      return { balance: this.balance, bank: this.bank };
    } catch (error) {
      console.error("Lỗi gửi tiền:", error);
      throw error;
    }
  }

  // Rút tiền từ ngân hàng
  async withdraw(amount) {
    try {
      if (amount <= 0) throw new Error("Số tiền phải lớn hơn 0");
      if (this.bank < amount)
        throw new Error("Số tiền trong ngân hàng không đủ");

      await db.run(
        `
                UPDATE users SET 
                    balance = balance + ?, 
                    bank = bank - ?,
                    updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            `,
        [amount, amount, this.id]
      );

      // Ghi log giao dịch
      await db.run(
        `
                INSERT INTO transactions (user_id, type, amount, description)
                VALUES (?, ?, ?, ?)
            `,
        [this.id, "withdraw", amount, `Rút tiền từ ngân hàng: ${amount}`]
      );

      this.balance += amount;
      this.bank -= amount;
      return { balance: this.balance, bank: this.bank };
    } catch (error) {
      console.error("Lỗi rút tiền:", error);
      throw error;
    }
  }

  // Cập nhật thời gian daily
  async updateLastDaily() {
    try {
      const now = new Date().toISOString();
      await db.run(
        "UPDATE users SET last_daily = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
        [now, this.id]
      );
      this.lastDaily = now;
    } catch (error) {
      console.error("Lỗi cập nhật last daily:", error);
      throw error;
    }
  }

  // Cập nhật thời gian work
  async updateLastWork() {
    try {
      const now = new Date().toISOString();
      await db.run(
        "UPDATE users SET last_work = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
        [now, this.id]
      );
      this.lastWork = now;
    } catch (error) {
      console.error("Lỗi cập nhật last work:", error);
      throw error;
    }
  }

  // Lấy tổng tài sản
  getTotalWealth() {
    return this.balance + this.bank;
  }

  // Lấy top users theo tổng tài sản
  static async getLeaderboard(limit = 10) {
    try {
      const users = await db.all(
        `
                SELECT id, username, balance, bank, (balance + bank) as total_wealth, level, prestige
                FROM users
                ORDER BY total_wealth DESC
                LIMIT ?
            `,
        [limit]
      );

      return users;
    } catch (error) {
      console.error("Lỗi lấy leaderboard:", error);
      throw error;
    }
  }

  // Thêm kinh nghiệm và kiểm tra level up
  async addExperience(amount) {
    try {
      const newExp = this.experience + amount;
      const expNeeded = this.getExpNeededForLevel(this.level + 1);

      let leveledUp = false;
      let newLevel = this.level;

      if (newExp >= expNeeded) {
        newLevel = this.level + 1;
        leveledUp = true;

        // Thưởng level up
        const levelReward = newLevel * 1000;
        await this.updateBalance(levelReward, "level_up");
      }

      await db.run(
        `
                UPDATE users SET
                    experience = ?,
                    level = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `,
        [newExp, newLevel, this.id]
      );

      this.experience = newExp;
      this.level = newLevel;

      return { leveledUp, newLevel, reward: leveledUp ? newLevel * 1000 : 0 };
    } catch (error) {
      console.error("Lỗi thêm kinh nghiệm:", error);
      throw error;
    }
  }

  // Tính kinh nghiệm cần thiết cho level
  getExpNeededForLevel(level) {
    return Math.floor(100 * Math.pow(1.5, level - 1));
  }

  // Lấy thông tin level hiện tại
  getLevelInfo() {
    const currentLevelExp = this.getExpNeededForLevel(this.level);
    const nextLevelExp = this.getExpNeededForLevel(this.level + 1);
    const progressExp = this.experience - currentLevelExp;
    const neededExp = nextLevelExp - currentLevelExp;
    const progress = Math.floor((progressExp / neededExp) * 100);

    return {
      currentLevel: this.level,
      currentExp: this.experience,
      progressExp,
      neededExp,
      progress: Math.max(0, Math.min(100, progress)),
    };
  }

  // Cập nhật streak daily
  async updateDailyStreak() {
    try {
      const newStreak = this.streakDaily + 1;
      await db.run(
        `
                UPDATE users SET
                    streak_daily = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `,
        [newStreak, this.id]
      );

      this.streakDaily = newStreak;
      return newStreak;
    } catch (error) {
      console.error("Lỗi cập nhật daily streak:", error);
      throw error;
    }
  }

  // Lấy thành tựu của user
  async getAchievements() {
    try {
      const achievements = await db.all(
        `
                SELECT a.*, ua.unlocked_at
                FROM achievements a
                LEFT JOIN user_achievements ua ON a.id = ua.achievement_id AND ua.user_id = ?
                ORDER BY ua.unlocked_at DESC, a.id
            `,
        [this.id]
      );

      return achievements.map((ach) => ({
        ...ach,
        unlocked: !!ach.unlocked_at,
      }));
    } catch (error) {
      console.error("Lỗi lấy achievements:", error);
      throw error;
    }
  }

  // Kiểm tra và mở khóa thành tựu
  async checkAchievements() {
    try {
      const achievements = await db.all(
        `
                SELECT a.* FROM achievements a
                WHERE a.id NOT IN (
                    SELECT achievement_id FROM user_achievements WHERE user_id = ?
                )
            `,
        [this.id]
      );

      const unlockedAchievements = [];

      for (const achievement of achievements) {
        let conditionMet = false;

        switch (achievement.condition_type) {
          case "total_earned":
            conditionMet = this.totalEarned >= achievement.condition_value;
            break;
          case "total_wealth":
            conditionMet = this.getTotalWealth() >= achievement.condition_value;
            break;
          case "bank_total":
            conditionMet = this.bank >= achievement.condition_value;
            break;
          // Thêm các điều kiện khác nếu cần
        }

        if (conditionMet) {
          // Mở khóa thành tựu
          await db.run(
            `
                        INSERT INTO user_achievements (user_id, achievement_id)
                        VALUES (?, ?)
                    `,
            [this.id, achievement.id]
          );

          // Thưởng achievement
          if (achievement.reward > 0) {
            await this.updateBalance(achievement.reward, "achievement");
          }

          unlockedAchievements.push(achievement);
        }
      }

      return unlockedAchievements;
    } catch (error) {
      console.error("Lỗi kiểm tra achievements:", error);
      throw error;
    }
  }
}

module.exports = User;
