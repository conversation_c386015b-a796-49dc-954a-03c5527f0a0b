{"version": 3, "file": "internals.d.ts", "sourceRoot": "", "sources": ["internals.ts"], "names": [], "mappings": "AAAA,MAAM,MAAM,SAAS,CAAC,CAAC,IAAI;KACzB,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;CAC3B,CAAC;AAEF,MAAM,MAAM,kBAAkB,CAAC,CAAC,IAAI;KAClC,CAAC,IAAI,MAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACjC,CAAC;AAEF,MAAM,MAAM,qDAAqD,CAAC,IAAI,IAAI;KACxE,CAAC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,GAC7D,qDAAqD,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAC7D,qDAAqD,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS;CAC5E,CAAC;AAEF,MAAM,MAAM,cAAc,CAAC,IAAI,IAAI,qDAAqD,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAExG,MAAM,MAAM,eAAe,CAAC,IAAI,IAAI,QAAQ,CAAC;KAAG,CAAC,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;CAAE,CAAC,CAAC;AAEjG,MAAM,MAAM,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC;AAExH,MAAM,MAAM,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;AAC/B,MAAM,MAAM,iBAAiB,CAAC,CAAC,IAAI,CAAC,SAAS,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACxE;;GAEG;AACH,MAAM,MAAM,iBAAiB,CAAC,CAAC,EAAE,CAAC,SAAS,iBAAiB,CAAC,CAAC,CAAC,IAC9D,CAAC,SAAS,OAAO,GAChB,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,KAAK,GAC9B,KAAK,GACJ;KAAG,CAAC,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,GAC7C,KAAK,CAAC;AAET,MAAM,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAEvD;;GAEG;AACH,MAAM,MAAM,iBAAiB,CAAC,CAAC,EAAE,CAAC,SAAS,iBAAiB,CAAC,CAAC,CAAC,IAC9D,CAAC,SAAS,OAAO,GAAG;KAAG,CAAC,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,GAAG,KAAK,CAAC;AAE1E,MAAM,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAKvD,eAAO,MAAM,iBAAiB;gBACjB,MAAM;CAOlB,CAAC"}