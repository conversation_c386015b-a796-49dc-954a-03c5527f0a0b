const { SlashCommandBuilder } = require("discord.js");
const User = require("../models/User");
const { canClaimDaily, calculateDailyReward } = require("../utils/economy");
const {
  createSuccessEmbed,
  createErrorEmbed,
  formatCurrency,
} = require("../utils/embedBuilder");
const config = require("../config/config");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("daily")
    .setNameLocalizations({
      vi: "hangngay",
    })
    .setDescription("Nhận phần thưởng hàng ngày"),

  cooldown: 5,

  async execute(interaction) {
    try {
      // Tạo hoặc lấy thông tin user
      const userData = await User.findOrCreate(interaction.user);

      // Kiểm tra xem có thể claim daily không
      if (!canClaimDaily(userData.lastDaily)) {
        const errorEmbed = createErrorEmbed(
          "Đã nhận rồi!",
          `Bạn đã nhận phần thưởng hàng ngày rồi! Hãy quay lại vào ngày mai.\n\n` +
            `${config.emojis.info} **Mẹo:** Phần thưởng sẽ reset vào 00:00 (GMT+7)`
        );

        return await interaction.reply({
          embeds: [errorEmbed],
          ephemeral: true,
        });
      }

      // Tính toán phần thưởng với streak bonus
      let baseReward = calculateDailyReward();

      // Cập nhật streak
      const newStreak = await userData.updateDailyStreak();

      // Tính streak bonus
      let streakBonus = 0;
      if (newStreak >= 7) {
        streakBonus = Math.floor(baseReward * 0.5); // 50% bonus cho streak 7 ngày
      } else if (newStreak >= 3) {
        streakBonus = Math.floor(baseReward * 0.2); // 20% bonus cho streak 3 ngày
      }

      const totalReward = baseReward + streakBonus;

      // Cập nhật số dư và thời gian daily
      await userData.updateBalance(totalReward, "daily");
      await userData.updateLastDaily();

      // Thêm kinh nghiệm
      const expGained = Math.floor(totalReward / 20);
      const levelResult = await userData.addExperience(expGained);

      // Tạo embed thành công
      let description =
        `Bạn đã nhận được **${formatCurrency(totalReward)}**!\n\n` +
        `${config.emojis.money} **Số dư hiện tại:** ${formatCurrency(
          userData.balance + totalReward
        )}\n` +
        `${config.emojis.star} **Kinh nghiệm:** +${expGained} EXP\n` +
        `${config.emojis.gift} **Hẹn gặp lại:** Ngày mai cùng giờ`;

      if (levelResult.leveledUp) {
        description +=
          `\n\n🎉 **LEVEL UP!** Bạn đã lên level ${levelResult.newLevel}!\n` +
          `💰 **Thưởng level:** ${formatCurrency(levelResult.reward)}`;
      }

      const successEmbed = createSuccessEmbed(
        "Phần thưởng hàng ngày!",
        description
      );

      // Thêm thông tin streak
      let streakText = `**Streak hiện tại:** ${newStreak} ngày`;
      if (streakBonus > 0) {
        streakText += `\n**Streak bonus:** ${formatCurrency(streakBonus)}`;
      }
      if (newStreak < 7) {
        const daysToNext = newStreak < 3 ? 3 - newStreak : 7 - newStreak;
        const nextBonus = newStreak < 3 ? "20%" : "50%";
        streakText += `\n**Còn ${daysToNext} ngày để bonus ${nextBonus}**`;
      }

      successEmbed.addFields({
        name: `${config.emojis.star} Daily Streak`,
        value: streakText,
        inline: false,
      });

      await interaction.reply({ embeds: [successEmbed] });

      // Kiểm tra achievements
      const newAchievements = await userData.checkAchievements();
      if (newAchievements.length > 0) {
        // Có thể thêm thông báo achievement sau
      }
    } catch (error) {
      console.error("Lỗi lệnh daily:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi hệ thống",
        "Không thể nhận phần thưởng hàng ngày. Vui lòng thử lại sau."
      );

      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  },
};
