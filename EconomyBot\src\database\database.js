const sqlite3 = require("sqlite3").verbose();
const path = require("path");
const fs = require("fs");
const config = require("../config/config");

class Database {
  constructor() {
    this.db = null;
    this.init();
  }

  init() {
    // Tạo thư mục data nếu chưa tồn tại
    const dataDir = path.dirname(config.database.path);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Kết nối database
    this.db = new sqlite3.Database(config.database.path, (err) => {
      if (err) {
        console.error("Lỗi kết nối database:", err.message);
      } else {
        console.log("✅ Đã kết nối database SQLite thành công");
        this.createTables();
      }
    });
  }

  async createTables() {
    try {
      // Bảng users - Thông tin người dùng
      await this.run(`
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    username TEXT NOT NULL,
                    discriminator TEXT,
                    avatar TEXT,
                    balance INTEGER DEFAULT 0,
                    bank INTEGER DEFAULT 0,
                    total_earned INTEGER DEFAULT 0,
                    total_spent INTEGER DEFAULT 0,
                    last_daily TEXT,
                    last_work TEXT,
                    last_crime TEXT,
                    last_rob TEXT,
                    streak_daily INTEGER DEFAULT 0,
                    streak_work INTEGER DEFAULT 0,
                    level INTEGER DEFAULT 1,
                    experience INTEGER DEFAULT 0,
                    prestige INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

      // Bảng items - Vật phẩm trong shop
      await this.run(`
                CREATE TABLE IF NOT EXISTS items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    price INTEGER NOT NULL,
                    emoji TEXT,
                    category TEXT DEFAULT 'general',
                    buyable BOOLEAN DEFAULT 1,
                    sellable BOOLEAN DEFAULT 1,
                    sell_price INTEGER,
                    rarity TEXT DEFAULT 'common',
                    level_required INTEGER DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

      // Bảng inventory - Kho đồ của người dùng
      await this.run(`
                CREATE TABLE IF NOT EXISTS inventory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    item_id INTEGER NOT NULL,
                    quantity INTEGER DEFAULT 1,
                    acquired_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (item_id) REFERENCES items (id)
                )
            `);

      // Bảng transactions - Lịch sử giao dịch
      await this.run(`
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    type TEXT NOT NULL,
                    amount INTEGER NOT NULL,
                    description TEXT,
                    target_user_id TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            `);

      // Bảng daily_stats - Thống kê hàng ngày
      await this.run(`
                CREATE TABLE IF NOT EXISTS daily_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    total_users INTEGER DEFAULT 0,
                    total_transactions INTEGER DEFAULT 0,
                    total_currency_circulation INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

      // Bảng achievements - Thành tựu
      await this.run(`
                CREATE TABLE IF NOT EXISTS achievements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    emoji TEXT,
                    condition_type TEXT NOT NULL,
                    condition_value INTEGER NOT NULL,
                    reward INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

      // Bảng user_achievements - Thành tựu của người dùng
      await this.run(`
                CREATE TABLE IF NOT EXISTS user_achievements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    achievement_id INTEGER NOT NULL,
                    unlocked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (achievement_id) REFERENCES achievements (id)
                )
            `);

      console.log("✅ Đã tạo các bảng database thành công");
      await this.insertDefaultItems();
      await this.insertDefaultAchievements();
    } catch (error) {
      console.error("❌ Lỗi tạo bảng database:", error);
      throw error;
    }
  }

  async insertDefaultItems() {
    try {
      // Thêm một số vật phẩm mặc định vào shop
      const defaultItems = [
        // Food & Drinks
        {
          name: "Bánh mì",
          description: "Một ổ bánh mì thơm ngon",
          price: 50,
          emoji: "🥖",
          category: "food",
          sell_price: 25,
          rarity: "common",
          level_required: 1,
        },
        {
          name: "Cà phê",
          description: "Ly cà phê đen đá",
          price: 30,
          emoji: "☕",
          category: "drink",
          sell_price: 15,
          rarity: "common",
          level_required: 1,
        },
        {
          name: "Phở",
          description: "Tô phở bò nóng hổi",
          price: 80,
          emoji: "🍜",
          category: "food",
          sell_price: 40,
          rarity: "common",
          level_required: 1,
        },
        {
          name: "Trà sữa",
          description: "Ly trà sữa trân châu",
          price: 60,
          emoji: "🧋",
          category: "drink",
          sell_price: 30,
          rarity: "common",
          level_required: 1,
        },
        // Electronics
        {
          name: "Điện thoại",
          description: "Chiếc điện thoại thông minh",
          price: 5000,
          emoji: "📱",
          category: "electronics",
          sell_price: 2500,
          rarity: "uncommon",
          level_required: 5,
        },
        {
          name: "Laptop",
          description: "Máy tính xách tay hiện đại",
          price: 15000,
          emoji: "💻",
          category: "electronics",
          sell_price: 7500,
          rarity: "rare",
          level_required: 10,
        },
        {
          name: "Tai nghe",
          description: "Tai nghe không dây cao cấp",
          price: 2000,
          emoji: "🎧",
          category: "electronics",
          sell_price: 1000,
          rarity: "uncommon",
          level_required: 3,
        },
        // Vehicles
        {
          name: "Xe đạp",
          description: "Chiếc xe đạp thể thao",
          price: 3000,
          emoji: "🚲",
          category: "vehicle",
          sell_price: 1500,
          rarity: "common",
          level_required: 2,
        },
        {
          name: "Xe máy",
          description: "Chiếc xe máy Honda",
          price: 50000,
          emoji: "🏍️",
          category: "vehicle",
          sell_price: 25000,
          rarity: "rare",
          level_required: 15,
        },
        {
          name: "Ô tô",
          description: "Chiếc ô tô sedan",
          price: 200000,
          emoji: "🚗",
          category: "vehicle",
          sell_price: 100000,
          rarity: "epic",
          level_required: 25,
        },
        // Property
        {
          name: "Căn hộ",
          description: "Căn hộ chung cư 2 phòng ngủ",
          price: 500000,
          emoji: "🏠",
          category: "property",
          sell_price: 250000,
          rarity: "epic",
          level_required: 30,
        },
        {
          name: "Biệt thự",
          description: "Biệt thự sang trọng",
          price: 2000000,
          emoji: "🏰",
          category: "property",
          sell_price: 1000000,
          rarity: "legendary",
          level_required: 50,
        },
        // Luxury
        {
          name: "Đồng hồ vàng",
          description: "Đồng hồ Rolex vàng 18k",
          price: 100000,
          emoji: "⌚",
          category: "luxury",
          sell_price: 50000,
          rarity: "epic",
          level_required: 20,
        },
        {
          name: "Kim cương",
          description: "Viên kim cương quý hiếm",
          price: 1000000,
          emoji: "💎",
          category: "luxury",
          sell_price: 500000,
          rarity: "legendary",
          level_required: 40,
        },
      ];

      for (const item of defaultItems) {
        await this.run(
          `
                    INSERT OR IGNORE INTO items (name, description, price, emoji, category, sell_price, rarity, level_required)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                `,
          [
            item.name,
            item.description,
            item.price,
            item.emoji,
            item.category,
            item.sell_price,
            item.rarity,
            item.level_required,
          ]
        );
      }

      console.log("✅ Đã thêm vật phẩm mặc định vào shop");
    } catch (error) {
      console.error("❌ Lỗi thêm vật phẩm mặc định:", error);
    }
  }

  async insertDefaultAchievements() {
    try {
      const defaultAchievements = [
        {
          name: "Người mới bắt đầu",
          description: "Kiếm được 1,000 xu đầu tiên",
          emoji: "🌱",
          condition_type: "total_earned",
          condition_value: 1000,
          reward: 500,
        },
        {
          name: "Triệu phú",
          description: "Có tổng tài sản 1,000,000 xu",
          emoji: "💰",
          condition_type: "total_wealth",
          condition_value: 1000000,
          reward: 50000,
        },
        {
          name: "Chăm chỉ",
          description: "Làm việc 10 lần",
          emoji: "💪",
          condition_type: "work_count",
          condition_value: 10,
          reward: 1000,
        },
        {
          name: "Người tiết kiệm",
          description: "Gửi 100,000 xu vào ngân hàng",
          emoji: "🏦",
          condition_type: "bank_total",
          condition_value: 100000,
          reward: 5000,
        },
        {
          name: "Thương gia",
          description: "Mua 50 vật phẩm",
          emoji: "🛒",
          condition_type: "items_bought",
          condition_value: 50,
          reward: 10000,
        },
      ];

      for (const achievement of defaultAchievements) {
        await this.run(
          `
                    INSERT OR IGNORE INTO achievements (name, description, emoji, condition_type, condition_value, reward)
                    VALUES (?, ?, ?, ?, ?, ?)
                `,
          [
            achievement.name,
            achievement.description,
            achievement.emoji,
            achievement.condition_type,
            achievement.condition_value,
            achievement.reward,
          ]
        );
      }

      console.log("✅ Đã thêm thành tựu mặc định");
    } catch (error) {
      console.error("❌ Lỗi thêm thành tựu mặc định:", error);
    }
  }

  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error("Lỗi đóng database:", err.message);
        } else {
          console.log("✅ Đã đóng kết nối database");
        }
      });
    }
  }

  // Phương thức để thực hiện query
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function (err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
}

module.exports = new Database();
