const { SlashCommandBuilder } = require("discord.js");
const User = require("../models/User");
const {
  createProfileEmbed,
  createErrorEmbed,
} = require("../utils/embedBuilder");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("balance")
    .setDescription("Kiểm tra số dư tài khoản của bạn hoặc người khác")
    .addUserOption((option) =>
      option
        .setName("user")
        .setDescription("Người dùng cần kiểm tra số dư")
        .setRequired(false)
    ),

  cooldown: 3,

  async execute(interaction) {
    try {
      const targetUser =
        interaction.options.getUser("user") || interaction.user;

      // Tạo hoặc lấy thông tin user
      const userData = await User.findOrCreate(targetUser);

      // Tạo embed profile
      const profileEmbed = createProfileEmbed(targetUser, userData);

      await interaction.reply({ embeds: [profileEmbed] });
    } catch (error) {
      console.error("Lỗi lệnh balance:", error);

      const errorEmbed = createErrorEmbed(
        "Lỗi",
        "Không thể lấy thông tin số dư. Vui lòng thử lại sau."
      );

      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  },
};
